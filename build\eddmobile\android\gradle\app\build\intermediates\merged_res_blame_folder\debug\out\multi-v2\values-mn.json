{"logs": [{"outputFile": "com.eddmanoo.eddmobile.app-mergeDebugResources-7:/values-mn/values-mn.xml", "map": [{"source": "/home/<USER>/.gradle/caches/transforms-3/8c3678815eba24d08bead1aab2683c40/transformed/appcompat-1.0.2/res/values-mn/values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,318,431,518,624,736,818,900,991,1084,1180,1276,1374,1467,1562,1654,1745,1835,1917,2026,2130,2227,2335,2436,2539,2698,2795", "endColumns": "112,99,112,86,105,111,81,81,90,92,95,95,97,92,94,91,90,89,81,108,103,96,107,100,102,158,96,80", "endOffsets": "213,313,426,513,619,731,813,895,986,1079,1175,1271,1369,1462,1557,1649,1740,1830,1912,2021,2125,2222,2330,2431,2534,2693,2790,2871"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/4f983ef72f5652104f8db50c8e36b4bf/transformed/core-1.1.0/res/values-mn/values-mn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2876", "endColumns": "100", "endOffsets": "2972"}}]}]}