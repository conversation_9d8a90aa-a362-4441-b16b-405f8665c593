[build-system]
requires = ["briefcase"]

[tool.briefcase]
project_name = "EDD Mobile"
bundle = "com.eddmanoo"
version = "1.0.0"
description = "Lista Cumparaturi. Aplicația oficială EDD & CO"
author = "EDD & CO"
author_email = "<EMAIL>"
url = "https://lista.eddmanoo.cloud"
license = {file = "LICENSE"}

[tool.briefcase.app.eddfitmobile]
formal_name = "Lista Cumparaturi"
description = "Lista Cumparaturi. Aplicația oficială EDD & CO"
sources = ["src/eddmobile"]
license = {file = "LICENSE"}
icon = "icon"
requires = []

[tool.briefcase.app.eddfitmobile.android]
permission.INTERNET = "Acces la internet pentru sincronizarea datelor Lista Cumparaturi"
permission.ACCESS_NETWORK_STATE = "Verificarea stării conexiunii la internet"
theme = "Theme.MaterialComponents.DayNight.NoActionBar"
requires = ["toga-android>=0.4.0"]


