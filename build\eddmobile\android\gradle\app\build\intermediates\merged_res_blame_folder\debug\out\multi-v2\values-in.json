{"logs": [{"outputFile": "com.eddmanoo.eddmobile.app-mergeDebugResources-7:/values-in/values-in.xml", "map": [{"source": "/home/<USER>/.gradle/caches/transforms-3/4f983ef72f5652104f8db50c8e36b4bf/transformed/core-1.1.0/res/values-in/values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2887", "endColumns": "100", "endOffsets": "2983"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/8c3678815eba24d08bead1aab2683c40/transformed/appcompat-1.0.2/res/values-in/values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,317,422,509,613,729,811,890,981,1074,1170,1264,1365,1458,1553,1647,1738,1829,1915,2018,2127,2228,2332,2440,2548,2704,2803", "endColumns": "109,101,104,86,103,115,81,78,90,92,95,93,100,92,94,93,90,90,85,102,108,100,103,107,107,155,98,83", "endOffsets": "210,312,417,504,608,724,806,885,976,1069,1165,1259,1360,1453,1548,1642,1733,1824,1910,2013,2122,2223,2327,2435,2543,2699,2798,2882"}}]}]}