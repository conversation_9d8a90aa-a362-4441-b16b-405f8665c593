{"logs": [{"outputFile": "com.eddmanoo.eddmobile.app-mergeDebugResources-7:/values-bn/values-bn.xml", "map": [{"source": "/home/<USER>/.gradle/caches/transforms-3/8c3678815eba24d08bead1aab2683c40/transformed/appcompat-1.0.2/res/values-bn/values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,326,432,526,631,760,838,916,1007,1100,1195,1289,1390,1483,1578,1672,1763,1854,1941,2051,2159,2258,2368,2474,2587,2752,2857", "endColumns": "108,111,105,93,104,128,77,77,90,92,94,93,100,92,94,93,90,90,86,109,107,98,109,105,112,164,104,81", "endOffsets": "209,321,427,521,626,755,833,911,1002,1095,1190,1284,1385,1478,1573,1667,1758,1849,1936,2046,2154,2253,2363,2469,2582,2747,2852,2934"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/4f983ef72f5652104f8db50c8e36b4bf/transformed/core-1.1.0/res/values-bn/values-bn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2939", "endColumns": "100", "endOffsets": "3035"}}]}]}