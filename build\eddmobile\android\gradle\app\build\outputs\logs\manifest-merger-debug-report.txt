-- Merging decision tree log ---
manifest
ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:2:1-39:12
INJECTED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:2:1-39:12
INJECTED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:2:1-39:12
INJECTED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:2:1-39:12
MERGED from [androidx.appcompat:appcompat:1.0.2] /home/<USER>/.gradle/caches/transforms-3/8c3678815eba24d08bead1aab2683c40/transformed/appcompat-1.0.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.constraintlayout:constraintlayout:1.1.3] /home/<USER>/.gradle/caches/transforms-3/d3b2b533a73868eb4d1c3b8716fee0eb/transformed/constraintlayout-1.1.3/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.fragment:fragment:1.0.0] /home/<USER>/.gradle/caches/transforms-3/03a61879e7a2bf456c31c8eb0751f2d5/transformed/fragment-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.0.0] /home/<USER>/.gradle/caches/transforms-3/472132f0bd2af432afb996bdb41b10bc/transformed/vectordrawable-animated-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /home/<USER>/.gradle/caches/transforms-3/397ad49d880b8b506af1e81b1506032f/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /home/<USER>/.gradle/caches/transforms-3/ccb1fdec34bc746cb6c914d8dc72a0f8/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /home/<USER>/.gradle/caches/transforms-3/9ee55494ba836944555ebe3a17d74f10/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable:1.0.1] /home/<USER>/.gradle/caches/transforms-3/2767e9d03fca79d3cd419110ec39a873/transformed/vectordrawable-1.0.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /home/<USER>/.gradle/caches/transforms-3/27ffbf6a47c9edc0eb8f26c6909ccc19/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /home/<USER>/.gradle/caches/transforms-3/b9b20b30d608b58950351e18e5611af7/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /home/<USER>/.gradle/caches/transforms-3/2a3c96f85a0d3ca7e7cf469c06bb9895/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /home/<USER>/.gradle/caches/transforms-3/0bd5a987d7f41494fc7ebdcdcd9f6df9/transformed/drawerlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /home/<USER>/.gradle/caches/transforms-3/c0f4f9a8dfe3dbb3a63b830efe44425e/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] /home/<USER>/.gradle/caches/transforms-3/5fe5697824aacbab581b8cc7c9911b87/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /home/<USER>/.gradle/caches/transforms-3/da893d22a282342de3bcfbae42c17178/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.1.0] /home/<USER>/.gradle/caches/transforms-3/4f983ef72f5652104f8db50c8e36b4bf/transformed/core-1.1.0/AndroidManifest.xml:17:1-26:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] /home/<USER>/.gradle/caches/transforms-3/e41d62a7882381afc23a3c6c6830e8df/transformed/versionedparcelable-1.1.0/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /home/<USER>/.gradle/caches/transforms-3/91a45b5c11b3b85852a5a7f8ba0ba71a/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /home/<USER>/.gradle/caches/transforms-3/57a26fa26d7d6f3bbff510501fd38b6b/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /home/<USER>/.gradle/caches/transforms-3/ec125f1330ec0adf1ac617fb4610a211/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /home/<USER>/.gradle/caches/transforms-3/1274ca59066cb99edca89812bbf35faf/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /home/<USER>/.gradle/caches/transforms-3/ece6a73c5c8eb7b61b6acde4eb63980e/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.0.0] /home/<USER>/.gradle/caches/transforms-3/f414159c34187a6cc489926712bef7c5/transformed/lifecycle-viewmodel-2.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.0.0] /home/<USER>/.gradle/caches/transforms-3/b173458387ebc4c6ef9e751eabf1723c/transformed/lifecycle-runtime-2.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /home/<USER>/.gradle/caches/transforms-3/5e1b5d0c3d6d8a8dfb89a814a1120535/transformed/lifecycle-livedata-2.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] /home/<USER>/.gradle/caches/transforms-3/a9fa1f00a90f3521ca0dac592f305411/transformed/lifecycle-livedata-core-2.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.0.0] /home/<USER>/.gradle/caches/transforms-3/2e1c0b8913ac08102107855dbe5412c6/transformed/core-runtime-2.0.0/AndroidManifest.xml:17:1-22:12
	package
		INJECTED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml
	android:versionCode
		INJECTED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:3:5-67
	android:name
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:3:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:4:5-79
	android:name
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:4:22-76
uses-permission#INTERNET
ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:5:5-48
	android:name
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:5:22-45
uses-permission#ACCESS_NETWORK_STATE
ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:6:5-60
	android:name
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:6:22-57
application
ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:7:5-37:19
INJECTED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:7:5-37:19
MERGED from [androidx.constraintlayout:constraintlayout:1.1.3] /home/<USER>/.gradle/caches/transforms-3/d3b2b533a73868eb4d1c3b8716fee0eb/transformed/constraintlayout-1.1.3/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:1.1.3] /home/<USER>/.gradle/caches/transforms-3/d3b2b533a73868eb4d1c3b8716fee0eb/transformed/constraintlayout-1.1.3/AndroidManifest.xml:9:5-20
MERGED from [androidx.core:core:1.1.0] /home/<USER>/.gradle/caches/transforms-3/4f983ef72f5652104f8db50c8e36b4bf/transformed/core-1.1.0/AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.1.0] /home/<USER>/.gradle/caches/transforms-3/4f983ef72f5652104f8db50c8e36b4bf/transformed/core-1.1.0/AndroidManifest.xml:24:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] /home/<USER>/.gradle/caches/transforms-3/e41d62a7882381afc23a3c6c6830e8df/transformed/versionedparcelable-1.1.0/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] /home/<USER>/.gradle/caches/transforms-3/e41d62a7882381afc23a3c6c6830e8df/transformed/versionedparcelable-1.1.0/AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.1.0] /home/<USER>/.gradle/caches/transforms-3/4f983ef72f5652104f8db50c8e36b4bf/transformed/core-1.1.0/AndroidManifest.xml:24:18-86
	android:supportsRtl
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:13:9-35
	android:label
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:10:9-44
	android:roundIcon
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:12:9-54
	android:icon
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:9:9-43
	android:allowBackup
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:8:9-35
	android:theme
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:14:9-49
	android:networkSecurityConfig
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:11:9-69
activity#org.beeware.android.MainActivity
ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:16:9-25:20
	android:exported
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:19:13-36
	android:configChanges
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:17:13-87
	android:name
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:18:13-60
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:20:13-23:29
action#android.intent.action.MAIN
ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:21:17-69
	android:name
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:21:25-66
category#android.intent.category.LAUNCHER
ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:22:17-77
	android:name
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:22:27-74
provider#androidx.core.content.FileProvider
ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:26:9-35:20
	android:grantUriPermissions
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:30:13-47
	android:authorities
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:28:13-70
	android:exported
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:29:13-37
	android:name
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:27:13-62
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:31:13-34:25
	android:resource
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:33:17-51
	android:name
		ADDED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:32:17-67
uses-sdk
INJECTED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml
INJECTED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml
MERGED from [androidx.appcompat:appcompat:1.0.2] /home/<USER>/.gradle/caches/transforms-3/8c3678815eba24d08bead1aab2683c40/transformed/appcompat-1.0.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.0.2] /home/<USER>/.gradle/caches/transforms-3/8c3678815eba24d08bead1aab2683c40/transformed/appcompat-1.0.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:1.1.3] /home/<USER>/.gradle/caches/transforms-3/d3b2b533a73868eb4d1c3b8716fee0eb/transformed/constraintlayout-1.1.3/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:1.1.3] /home/<USER>/.gradle/caches/transforms-3/d3b2b533a73868eb4d1c3b8716fee0eb/transformed/constraintlayout-1.1.3/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.fragment:fragment:1.0.0] /home/<USER>/.gradle/caches/transforms-3/03a61879e7a2bf456c31c8eb0751f2d5/transformed/fragment-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.0.0] /home/<USER>/.gradle/caches/transforms-3/03a61879e7a2bf456c31c8eb0751f2d5/transformed/fragment-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.0.0] /home/<USER>/.gradle/caches/transforms-3/472132f0bd2af432afb996bdb41b10bc/transformed/vectordrawable-animated-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.0.0] /home/<USER>/.gradle/caches/transforms-3/472132f0bd2af432afb996bdb41b10bc/transformed/vectordrawable-animated-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /home/<USER>/.gradle/caches/transforms-3/397ad49d880b8b506af1e81b1506032f/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /home/<USER>/.gradle/caches/transforms-3/397ad49d880b8b506af1e81b1506032f/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /home/<USER>/.gradle/caches/transforms-3/ccb1fdec34bc746cb6c914d8dc72a0f8/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /home/<USER>/.gradle/caches/transforms-3/ccb1fdec34bc746cb6c914d8dc72a0f8/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /home/<USER>/.gradle/caches/transforms-3/9ee55494ba836944555ebe3a17d74f10/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /home/<USER>/.gradle/caches/transforms-3/9ee55494ba836944555ebe3a17d74f10/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable:1.0.1] /home/<USER>/.gradle/caches/transforms-3/2767e9d03fca79d3cd419110ec39a873/transformed/vectordrawable-1.0.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable:1.0.1] /home/<USER>/.gradle/caches/transforms-3/2767e9d03fca79d3cd419110ec39a873/transformed/vectordrawable-1.0.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /home/<USER>/.gradle/caches/transforms-3/27ffbf6a47c9edc0eb8f26c6909ccc19/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /home/<USER>/.gradle/caches/transforms-3/27ffbf6a47c9edc0eb8f26c6909ccc19/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /home/<USER>/.gradle/caches/transforms-3/b9b20b30d608b58950351e18e5611af7/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /home/<USER>/.gradle/caches/transforms-3/b9b20b30d608b58950351e18e5611af7/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /home/<USER>/.gradle/caches/transforms-3/2a3c96f85a0d3ca7e7cf469c06bb9895/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /home/<USER>/.gradle/caches/transforms-3/2a3c96f85a0d3ca7e7cf469c06bb9895/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /home/<USER>/.gradle/caches/transforms-3/0bd5a987d7f41494fc7ebdcdcd9f6df9/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /home/<USER>/.gradle/caches/transforms-3/0bd5a987d7f41494fc7ebdcdcd9f6df9/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /home/<USER>/.gradle/caches/transforms-3/c0f4f9a8dfe3dbb3a63b830efe44425e/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /home/<USER>/.gradle/caches/transforms-3/c0f4f9a8dfe3dbb3a63b830efe44425e/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /home/<USER>/.gradle/caches/transforms-3/5fe5697824aacbab581b8cc7c9911b87/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /home/<USER>/.gradle/caches/transforms-3/5fe5697824aacbab581b8cc7c9911b87/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /home/<USER>/.gradle/caches/transforms-3/da893d22a282342de3bcfbae42c17178/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /home/<USER>/.gradle/caches/transforms-3/da893d22a282342de3bcfbae42c17178/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.1.0] /home/<USER>/.gradle/caches/transforms-3/4f983ef72f5652104f8db50c8e36b4bf/transformed/core-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.1.0] /home/<USER>/.gradle/caches/transforms-3/4f983ef72f5652104f8db50c8e36b4bf/transformed/core-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] /home/<USER>/.gradle/caches/transforms-3/e41d62a7882381afc23a3c6c6830e8df/transformed/versionedparcelable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] /home/<USER>/.gradle/caches/transforms-3/e41d62a7882381afc23a3c6c6830e8df/transformed/versionedparcelable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /home/<USER>/.gradle/caches/transforms-3/91a45b5c11b3b85852a5a7f8ba0ba71a/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /home/<USER>/.gradle/caches/transforms-3/91a45b5c11b3b85852a5a7f8ba0ba71a/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /home/<USER>/.gradle/caches/transforms-3/57a26fa26d7d6f3bbff510501fd38b6b/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /home/<USER>/.gradle/caches/transforms-3/57a26fa26d7d6f3bbff510501fd38b6b/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /home/<USER>/.gradle/caches/transforms-3/ec125f1330ec0adf1ac617fb4610a211/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /home/<USER>/.gradle/caches/transforms-3/ec125f1330ec0adf1ac617fb4610a211/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /home/<USER>/.gradle/caches/transforms-3/1274ca59066cb99edca89812bbf35faf/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /home/<USER>/.gradle/caches/transforms-3/1274ca59066cb99edca89812bbf35faf/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /home/<USER>/.gradle/caches/transforms-3/ece6a73c5c8eb7b61b6acde4eb63980e/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /home/<USER>/.gradle/caches/transforms-3/ece6a73c5c8eb7b61b6acde4eb63980e/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.0.0] /home/<USER>/.gradle/caches/transforms-3/f414159c34187a6cc489926712bef7c5/transformed/lifecycle-viewmodel-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.0.0] /home/<USER>/.gradle/caches/transforms-3/f414159c34187a6cc489926712bef7c5/transformed/lifecycle-viewmodel-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.0.0] /home/<USER>/.gradle/caches/transforms-3/b173458387ebc4c6ef9e751eabf1723c/transformed/lifecycle-runtime-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.0.0] /home/<USER>/.gradle/caches/transforms-3/b173458387ebc4c6ef9e751eabf1723c/transformed/lifecycle-runtime-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /home/<USER>/.gradle/caches/transforms-3/5e1b5d0c3d6d8a8dfb89a814a1120535/transformed/lifecycle-livedata-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /home/<USER>/.gradle/caches/transforms-3/5e1b5d0c3d6d8a8dfb89a814a1120535/transformed/lifecycle-livedata-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] /home/<USER>/.gradle/caches/transforms-3/a9fa1f00a90f3521ca0dac592f305411/transformed/lifecycle-livedata-core-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] /home/<USER>/.gradle/caches/transforms-3/a9fa1f00a90f3521ca0dac592f305411/transformed/lifecycle-livedata-core-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.0.0] /home/<USER>/.gradle/caches/transforms-3/2e1c0b8913ac08102107855dbe5412c6/transformed/core-runtime-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.0.0] /home/<USER>/.gradle/caches/transforms-3/2e1c0b8913ac08102107855dbe5412c6/transformed/core-runtime-2.0.0/AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml
