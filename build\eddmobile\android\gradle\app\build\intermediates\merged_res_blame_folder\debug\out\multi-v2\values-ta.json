{"logs": [{"outputFile": "com.eddmanoo.eddmobile.app-mergeDebugResources-7:/values-ta/values-ta.xml", "map": [{"source": "/home/<USER>/.gradle/caches/transforms-3/8c3678815eba24d08bead1aab2683c40/transformed/appcompat-1.0.2/res/values-ta/values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,317,432,521,628,754,832,909,1009,1114,1210,1305,1412,1514,1618,1713,1815,1913,1995,2097,2201,2298,2408,2510,2617,2774,2874", "endColumns": "113,97,114,88,106,125,77,76,99,104,95,94,106,101,103,94,101,97,81,101,103,96,109,101,106,156,99,79", "endOffsets": "214,312,427,516,623,749,827,904,1004,1109,1205,1300,1407,1509,1613,1708,1810,1908,1990,2092,2196,2293,2403,2505,2612,2769,2869,2949"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/4f983ef72f5652104f8db50c8e36b4bf/transformed/core-1.1.0/res/values-ta/values-ta.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2954", "endColumns": "100", "endOffsets": "3050"}}]}]}