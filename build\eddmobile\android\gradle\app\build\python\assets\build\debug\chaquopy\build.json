{"assets": {"bootstrap-native/armeabi-v7a/_random.cpython-310.so": "072edf2733f79d18c7b96d06c2f2a49ca5cddbe5", "bootstrap-native/armeabi-v7a/mmap.cpython-310.so": "a04a35d5884437d3ebb2fe9360b886806b65857a", "bootstrap-native/x86_64/_ctypes.cpython-310.so": "76bd21b6cbac479eb1c4d2291297382a458d5997", "bootstrap-native/x86_64/_struct.cpython-310.so": "f739244d961e8372b043ea6e162361fe4d9fcab6", "bootstrap-native/x86_64/zlib.cpython-310.so": "2c06fb647bec4cafae0e3ed330b34dca848977c4", "bootstrap-native/x86_64/_lzma.cpython-310.so": "2a4e47330d1dc44221530125a270b94f6489092b", "bootstrap-native/arm64-v8a/math.cpython-310.so": "f537162b565e25bb9b47e17ee6c33d41ac2bb0a5", "requirements-x86_64.imy": "b04f3ee8f5e43fa3b162981b50bb72fe1acabb33", "app.imy": "89bde507921236d25e4299447c456457ec04656e", "stdlib-common.imy": "8eddcaeafd7580fbc906453ee68620cc81ac6820", "bootstrap-native/x86_64/_datetime.cpython-310.so": "dd26445eca876f88c2d12f18ba0b2c6df0f8b53f", "bootstrap-native/arm64-v8a/zlib.cpython-310.so": "57828cf6bad24f90ae378b34e48e646e86c4f91c", "bootstrap-native/arm64-v8a/_random.cpython-310.so": "16fe19eded68b027fd30e2c839af3eed475e0af0", "bootstrap-native/armeabi-v7a/java/chaquopy.so": "41b5bf1d9614ee75f398a1331c5cc060ceac48a2", "stdlib-arm64-v8a.imy": "0b400f06242a2a4e53d2f7723aca706e9a9c6e25", "stdlib-armeabi-v7a.imy": "b3e2b53837b66ce9c497ffad5b73a6867fecc1b2", "bootstrap-native/armeabi-v7a/_datetime.cpython-310.so": "7db836351f16317f912d448eb1b6a69aeec58d41", "bootstrap-native/x86_64/_bz2.cpython-310.so": "4efc33c165339e5556082489a821c837b8d72104", "bootstrap-native/armeabi-v7a/_sha512.cpython-310.so": "9569db7c4aea2fd9e130007066a4d15668580b89", "bootstrap-native/x86_64/java/chaquopy.so": "b90193f790b596b6778590a7c528aef0b7a09db4", "requirements-arm64-v8a.imy": "b04f3ee8f5e43fa3b162981b50bb72fe1acabb33", "bootstrap-native/arm64-v8a/mmap.cpython-310.so": "20892d5d51d3cbdb9713b72a8fb510a074ff38c6", "bootstrap-native/x86_64/math.cpython-310.so": "536a29a2229c774b3812c8243d61532ab71ac9e2", "bootstrap-native/arm64-v8a/java/chaquopy.so": "9028ef1a09cad6e0da246f247d854c985ea14336", "bootstrap-native/x86_64/binascii.cpython-310.so": "b343cbf55062a46d1d3b67ac0a1f3a1751ada8a6", "bootstrap-native/armeabi-v7a/zlib.cpython-310.so": "e2cad494a7b084ae63611b9901e969423c9a24aa", "bootstrap-native/arm64-v8a/_bz2.cpython-310.so": "148a8a5c0d0a7fc3ae5b9a95b676ed39b41839f8", "bootstrap.imy": "5fa23ea048ab480e34d9c918e31e7eb92bb72fde", "bootstrap-native/armeabi-v7a/_lzma.cpython-310.so": "da268b9d3bd446e1620e068e178162a3a5be2531", "bootstrap-native/arm64-v8a/_sha512.cpython-310.so": "0110f91f307ca392f345e861300a89c37503f929", "cacert.pem": "2c68212e96605210eddf740291862bdf59398aef", "bootstrap-native/x86_64/_random.cpython-310.so": "4389567346b55c557c5ef462c8ad73121d1b7e28", "bootstrap-native/arm64-v8a/_struct.cpython-310.so": "54cb639e505c6f1a4601226c401828f99dc1009f", "bootstrap-native/arm64-v8a/_ctypes.cpython-310.so": "366e6aed5d26099375d00c96c248a0c322dff6c1", "bootstrap-native/arm64-v8a/_datetime.cpython-310.so": "0173532e323246c4c1affedcc2de95b63e9fdf90", "bootstrap-native/armeabi-v7a/binascii.cpython-310.so": "d209ab08add93eaa848a509d2c49eb762e6404b6", "bootstrap-native/armeabi-v7a/_struct.cpython-310.so": "aec6dcb30f1b9728633d720fffe6677b555a2a52", "bootstrap-native/armeabi-v7a/_ctypes.cpython-310.so": "119eebf2e21a1fd071d6d9552cc001d06116ad23", "stdlib-x86_64.imy": "f6a3c536f40b96f375a43383fadda6dd73830dfb", "bootstrap-native/armeabi-v7a/math.cpython-310.so": "f9be902961685d2e031979f81210cc7efb29671a", "bootstrap-native/arm64-v8a/_lzma.cpython-310.so": "5be2a673230c6260086d100812a41cbec35d1eca", "requirements-armeabi-v7a.imy": "b04f3ee8f5e43fa3b162981b50bb72fe1acabb33", "bootstrap-native/armeabi-v7a/_bz2.cpython-310.so": "546b3a8c34165bcf74265d5c2c5ec27ead9de41a", "bootstrap-native/arm64-v8a/binascii.cpython-310.so": "17223aaac9b167b1dee785a195c8bc2fd275ae55", "bootstrap-native/x86_64/_sha512.cpython-310.so": "5b17efd678760fbece336094ce562e694b0c902b", "requirements-common.imy": "5742966a605af35c62269645d190ed9ed565b790", "bootstrap-native/x86_64/mmap.cpython-310.so": "b1a867a461ccfdf8bef9e214b3caacaa91c602af"}, "extract_packages": [], "python_version": "3.10"}