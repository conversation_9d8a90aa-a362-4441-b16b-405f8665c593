{"logs": [{"outputFile": "com.eddmanoo.eddmobile.app-mergeDebugResources-7:/values-v23/values-v23.xml", "map": [{"source": "/home/<USER>/.gradle/caches/transforms-3/8c3678815eba24d08bead1aab2683c40/transformed/appcompat-1.0.2/res/values-v23/values-v23.xml", "from": {"startLines": "2,3,4,5,6,19,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1277,2079,2206,2311,2426,2533", "endLines": "2,3,4,5,18,31,32,33,34,35,36", "endColumns": "134,134,74,86,12,12,126,104,114,106,112", "endOffsets": "185,320,395,482,1272,2074,2201,2306,2421,2528,2641"}, "to": {"startLines": "2,3,4,5,6,19,32,33,34,35,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1131,1787,1914,2019,2134,2241", "endLines": "2,3,4,5,18,31,32,33,34,35,36", "endColumns": "134,134,74,86,12,12,126,104,114,106,112", "endOffsets": "185,320,395,482,1126,1782,1909,2014,2129,2236,2349"}}]}]}