<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res"><file name="splash_screen_background" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/drawable/splash_screen_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-anydpi-v26/ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-anydpi-v26/ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-hdpi/ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-hdpi/ic_launcher_foreground.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-hdpi/ic_launcher_round.png" qualifiers="hdpi-v4" type="mipmap"/><file name="splash" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-hdpi/splash.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-mdpi/ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-mdpi/ic_launcher_foreground.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-mdpi/ic_launcher_round.png" qualifiers="mdpi-v4" type="mipmap"/><file name="splash" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-mdpi/splash.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-xhdpi/ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-xhdpi/ic_launcher_foreground.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-xhdpi/ic_launcher_round.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="splash" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-xhdpi/splash.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-xxhdpi/ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-xxhdpi/ic_launcher_foreground.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-xxhdpi/ic_launcher_round.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="splash" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-xxhdpi/splash.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-xxxhdpi/ic_launcher_foreground.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-xxxhdpi/ic_launcher_round.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="splash" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/mipmap-xxxhdpi/splash.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/values/briefcase.xml" qualifiers=""><string name="main_module">eddmobile</string></file><file path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/values/colors.xml" qualifiers=""><color name="colorPrimary">#008577</color><color name="colorPrimaryDark">#00574B</color><color name="colorAccent">#D81B60</color><color name="colorSplashScreenBackground">#FFFFFF</color></file><file path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/values/strings.xml" qualifiers=""><string name="formal_name">"Lista Cumparaturi Mobile"</string><string name="app_name">eddmobile</string></file><file path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/values/styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style><style name="AppTheme.Launcher">
        <item name="android:windowBackground">@drawable/splash_screen_background</item>
    </style></file><file name="file_paths" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/xml/file_paths.xml" qualifiers="" type="xml"/><file name="network_security_config" path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/res/xml/network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/build/generated/res/resValues/debug"/></dataSet><mergedItems/></merger>