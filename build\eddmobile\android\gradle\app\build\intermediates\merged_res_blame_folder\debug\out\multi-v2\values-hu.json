{"logs": [{"outputFile": "com.eddmanoo.eddmobile.app-mergeDebugResources-7:/values-hu/values-hu.xml", "map": [{"source": "/home/<USER>/.gradle/caches/transforms-3/8c3678815eba24d08bead1aab2683c40/transformed/appcompat-1.0.2/res/values-hu/values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,433,517,629,759,835,911,1002,1095,1191,1285,1386,1479,1574,1669,1760,1851,1934,2044,2155,2255,2366,2474,2593,2775,2878", "endColumns": "107,104,114,83,111,129,75,75,90,92,95,93,100,92,94,94,90,90,82,109,110,99,110,107,118,181,102,82", "endOffsets": "208,313,428,512,624,754,830,906,997,1090,1186,1280,1381,1474,1569,1664,1755,1846,1929,2039,2150,2250,2361,2469,2588,2770,2873,2956"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/4f983ef72f5652104f8db50c8e36b4bf/transformed/core-1.1.0/res/values-hu/values-hu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2961", "endColumns": "100", "endOffsets": "3057"}}]}]}