import toga
from toga.style import Pack
from toga.style.pack import COLUMN, ROW


class EDDmobileMobile(toga.App):
    def startup(self):
        """Construiește interfața aplicației - Browser dedicat EDDmobile"""

        # Container principal
        main_box = toga.Box(style=Pack(direction=COLUMN, padding=0))

        # Header compact cu butoanele de navigație - culoare bleumarin
        header_box = toga.Box(style=Pack(
            direction=ROW,
            padding=(8, 10, 8, 10),
            background_color='#1e3a8a'  # Bleumarin închis
        ))

        # Buton înapoi/home
        self.home_button = toga.Button(
            '🏠',
            on_press=self.go_home,
            style=Pack(
                padding=(0, 8, 0, 0),
                width=50,
                background_color='#3b82f6',  # Bleumarin mai deschis
                color='white'
            )
        )

        # Titlu aplicației
        self.title_label = toga.Label(
            'EDDmobile',
            style=Pack(
                flex=1,
                text_align='center',
                font_size=16,
                font_weight='bold',
                color='white'  # Text alb pe fundal bleumarin
            )
        )

        # Buton refresh
        self.refresh_button = toga.Button(
            '↻',
            on_press=self.refresh_webview,
            style=Pack(
                padding=(0, 0, 0, 8),
                width=50,
                background_color='#3b82f6',  # Bleumarin mai deschis
                color='white'
            )
        )

        # Adaugă butoanele la header
        header_box.add(self.home_button)
        header_box.add(self.title_label)
        header_box.add(self.refresh_button)

        # WebView pentru EDDmobile - ocupă tot spațiul disponibil
        self.webview = toga.WebView(
            style=Pack(flex=1, padding=0)
        )

        # Adaugă header și webview la container principal
        main_box.add(header_box)
        main_box.add(self.webview)

        # Creează fereastra principală
        self.main_window = toga.MainWindow(title=self.formal_name)
        self.main_window.content = main_box
        self.main_window.show()

        # Încarcă automat site-ul EDDmobile la pornire
        self.load_EDDmobile()

    def load_EDDmobile(self):
        """Încarcă site-ul EDDmobile în WebView"""
        try:
            self.webview.url = 'https://lista.eddmanoo.cloud'
            self.title_label.text = 'Lista Cumparaturi'
        except Exception as e:
            print(f"Eroare la încărcarea Lista Cumparaturi: {e}")
            self.title_label.text = 'Eroare de încărcare'

    def go_home(self, widget):
        """Reîncarcă pagina principală EDDmobile"""
        self.load_EDDmobile()

    def refresh_webview(self, widget):
        """Reîncarcă pagina web curentă"""
        try:
            # Reîncarcă pagina curentă
            current_url = self.webview.url
            if current_url:
                self.webview.url = current_url
            else:
                # Dacă nu există URL, încarcă pagina principală
                self.load_EDDmobile()
        except Exception as e:
            print(f"Eroare la reîncărcarea paginii: {e}")
            # În caz de eroare, încearcă să reîncarce pagina principală
            self.load_EDDmobile()


def main():
    return EDDmobileMobile()


if __name__ == '__main__':
    main().main_loop()
