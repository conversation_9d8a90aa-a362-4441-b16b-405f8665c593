1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.eddmanoo.eddmobile"
4    android:versionCode="1000000"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:3:5-67
11-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:4:5-79
12-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:4:22-76
13    <uses-permission android:name="INTERNET" />
13-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:5:5-48
13-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:5:22-45
14    <uses-permission android:name="ACCESS_NETWORK_STATE" />
14-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:6:5-60
14-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:6:22-57
15
16    <application
16-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:7:5-37:19
17        android:allowBackup="true"
17-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:8:9-35
18        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
18-->[androidx.core:core:1.1.0] /home/<USER>/.gradle/caches/transforms-3/4f983ef72f5652104f8db50c8e36b4bf/transformed/core-1.1.0/AndroidManifest.xml:24:18-86
19        android:debuggable="true"
20        android:extractNativeLibs="false"
21        android:icon="@mipmap/ic_launcher"
21-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:9:9-43
22        android:label="@string/formal_name"
22-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:10:9-44
23        android:networkSecurityConfig="@xml/network_security_config"
23-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:11:9-69
24        android:roundIcon="@mipmap/ic_launcher_round"
24-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:12:9-54
25        android:supportsRtl="true"
25-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:13:9-35
26        android:theme="@style/AppTheme.Launcher" >
26-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:14:9-49
27
28        <!-- https://developer.android.com/guide/topics/resources/runtime-changes#HandlingTheChange -->
29        <activity
29-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:16:9-25:20
30            android:name="org.beeware.android.MainActivity"
30-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:18:13-60
31            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
31-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:17:13-87
32            android:exported="true" >
32-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:19:13-36
33            <intent-filter>
33-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:20:13-23:29
34                <action android:name="android.intent.action.MAIN" />
34-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:21:17-69
34-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:21:25-66
35
36                <category android:name="android.intent.category.LAUNCHER" />
36-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:22:17-77
36-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:22:27-74
37            </intent-filter>
38        </activity>
39
40        <provider
40-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:26:9-35:20
41            android:name="androidx.core.content.FileProvider"
41-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:27:13-62
42            android:authorities="com.eddmanoo.eddmobile.fileprovider"
42-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:28:13-70
43            android:exported="false"
43-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:29:13-37
44            android:grantUriPermissions="true" >
44-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:30:13-47
45            <meta-data
45-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:31:13-34:25
46                android:name="android.support.FILE_PROVIDER_PATHS"
46-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:32:17-67
47                android:resource="@xml/file_paths" >
47-->/mnt/c/Users/<USER>/Desktop/lista.apk/build/eddmobile/android/gradle/app/src/main/AndroidManifest.xml:33:17-51
48            </meta-data>
49        </provider>
50    </application>
51
52</manifest>
