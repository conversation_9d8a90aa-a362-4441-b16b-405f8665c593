{"logs": [{"outputFile": "com.eddmanoo.eddmobile.app-mergeDebugResources-7:/values-hy/values-hy.xml", "map": [{"source": "/home/<USER>/.gradle/caches/transforms-3/4f983ef72f5652104f8db50c8e36b4bf/transformed/core-1.1.0/res/values-hy/values-hy.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "30", "startColumns": "4", "startOffsets": "2884", "endColumns": "100", "endOffsets": "2980"}}, {"source": "/home/<USER>/.gradle/caches/transforms-3/8c3678815eba24d08bead1aab2683c40/transformed/appcompat-1.0.2/res/values-hy/values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,309,419,508,614,729,811,892,983,1076,1172,1266,1367,1460,1555,1649,1740,1831,1916,2023,2130,2229,2339,2446,2546,2703,2802", "endColumns": "102,100,109,88,105,114,81,80,90,92,95,93,100,92,94,93,90,90,84,106,106,98,109,106,99,156,98,81", "endOffsets": "203,304,414,503,609,724,806,887,978,1071,1167,1261,1362,1455,1550,1644,1735,1826,1911,2018,2125,2224,2334,2441,2541,2698,2797,2879"}}]}]}